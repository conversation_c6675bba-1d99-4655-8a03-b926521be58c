# Balkan Cloud Experts - Deployment Guide

## 🚀 Produktions-Deployment

### Voraussetzungen
- Node.js 18+ installiert
- npm oder yarn package manager
- Domain: balkancloudexperts.com
- SSL-Zertifikat
- Reverse Proxy (nginx empfohlen)

### 1. Server Setup

```bash
# 1. Repository klonen/hochladen
git clone <repository-url> /var/www/balkancloudexperts.com
cd /var/www/balkancloudexperts.com

# 2. Dependencies installieren
npm install --production

# 3. Environment Variablen konfigurieren
cp .env.example .env
nano .env
```

### 2. Environment Konfiguration (.env)

```bash
# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
EMAIL_TO=<EMAIL>

# Server Configuration
PORT=3001
NODE_ENV=production

# Website Configuration
SITE_URL=https://www.balkancloudexperts.com
SITE_NAME=Balkan Cloud Experts

# Security
SESSION_SECRET=your-secure-random-string-here
```

### 3. Nginx Konfiguration

```nginx
# /etc/nginx/sites-available/balkancloudexperts.com
server {
    listen 80;
    server_name balkancloudexperts.com www.balkancloudexperts.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name balkancloudexperts.com www.balkancloudexperts.com;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/balkancloudexperts.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/balkancloudexperts.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Main proxy
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|webp|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3001;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # API endpoints
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. SSL-Zertifikat (Let's Encrypt)

```bash
# Certbot installieren
sudo apt install certbot python3-certbot-nginx

# Zertifikat erstellen
sudo certbot --nginx -d balkancloudexperts.com -d www.balkancloudexperts.com

# Auto-Renewal testen
sudo certbot renew --dry-run
```

### 5. PM2 Process Manager

```bash
# PM2 installieren
npm install -g pm2

# Ecosystem Datei erstellen
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'balkancloudexperts',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF

# Logs Ordner erstellen
mkdir -p logs

# App starten
pm2 start ecosystem.config.js

# PM2 beim Boot starten
pm2 startup
pm2 save
```

### 6. Firewall Konfiguration

```bash
# UFW Firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 7. Monitoring & Logs

```bash
# PM2 Status
pm2 status
pm2 logs balkancloudexperts

# Nginx Logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# System Resources
htop
df -h
```

## 🔄 Updates & Maintenance

### Code Updates
```bash
# 1. Backup
cp -r /var/www/balkancloudexperts.com /var/www/backup-$(date +%Y%m%d)

# 2. Pull changes
cd /var/www/balkancloudexperts.com
git pull origin main

# 3. Install dependencies
npm install --production

# 4. Restart application
pm2 restart balkancloudexperts
```

### Database Backup (falls später hinzugefügt)
```bash
# Automatisches Backup Script
cat > /usr/local/bin/backup-balkancloudexperts.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/balkancloudexperts"
mkdir -p $BACKUP_DIR

# Code Backup
tar -czf $BACKUP_DIR/code_$DATE.tar.gz /var/www/balkancloudexperts.com

# Cleanup old backups (keep last 7 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /usr/local/bin/backup-balkancloudexperts.sh

# Cron Job für tägliches Backup
echo "0 2 * * * /usr/local/bin/backup-balkancloudexperts.sh" | sudo crontab -
```

## 📊 Performance Optimierung

### 1. Node.js Optimierung
```bash
# .env Ergänzungen
NODE_OPTIONS="--max-old-space-size=2048"
UV_THREADPOOL_SIZE=128
```

### 2. Nginx Caching
```nginx
# In nginx.conf
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g 
                 inactive=60m use_temp_path=off;

# In server block
location / {
    proxy_cache my_cache;
    proxy_cache_revalidate on;
    proxy_cache_min_uses 3;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
    proxy_cache_background_update on;
    proxy_cache_lock on;
    
    proxy_pass http://localhost:3001;
}
```

## 🔒 Sicherheit

### 1. Fail2Ban
```bash
sudo apt install fail2ban

# /etc/fail2ban/jail.local
[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
bantime = 3600
```

### 2. Automatische Updates
```bash
# Unattended upgrades
sudo apt install unattended-upgrades
sudo dpkg-reconfigure unattended-upgrades
```

## 📞 Support

Bei Problemen:
- **Email**: <EMAIL>
- **Logs prüfen**: `pm2 logs balkancloudexperts`
- **Status prüfen**: `pm2 status`
- **Restart**: `pm2 restart balkancloudexperts`

---

© 2025 SSC d.o.o. - Balkan Cloud Experts
