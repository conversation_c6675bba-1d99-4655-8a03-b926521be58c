/* Balkan Cloud Experts - Professional Tech Styles */
/* Modern, clean design for B2B cloud services */

[x-cloak] { 
    display: none !important;
}

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

:root {
    /* Primary Brand Colors - Tech/Cloud focused */
    --primary-color: #0f172a;        /* Slate 900 - Professional dark */
    --primary-light: #1e293b;        /* Slate 800 */
    --primary-dark: #020617;         /* Slate 950 */
    
    /* Accent Colors */
    --accent-blue: #0ea5e9;          /* Sky 500 - Cloud/Tech */
    --accent-cyan: #06b6d4;          /* Cyan 500 - Digital */
    --accent-green: #10b981;         /* Emerald 500 - Success */
    
    /* Text Colors */
    --text-dark: #1f2937;            /* Gray 800 */
    --text-medium: #4b5563;          /* Gray 600 */
    --text-light: #6b7280;           /* Gray 500 */
    --text-muted: #9ca3af;           /* Gray 400 */
    
    /* Background Colors */
    --bg-primary: #ffffff;           /* White */
    --bg-secondary: #f8fafc;         /* Slate 50 */
    --bg-tertiary: #f1f5f9;          /* Slate 100 */
    --bg-dark: #0f172a;              /* Slate 900 */
    
    /* Border Colors */
    --border-light: #e2e8f0;         /* Slate 200 */
    --border-medium: #cbd5e1;        /* Slate 300 */
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-dark);
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    font-weight: 600;
}

h3 {
    font-size: 1.5rem;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color);
}

.text-accent-blue {
    color: var(--accent-blue);
}

.text-accent-cyan {
    color: var(--accent-cyan);
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-accent-blue {
    background-color: var(--accent-blue);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.bg-gradient-tech {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
}

.border-primary {
    border-color: var(--primary-color);
}

/* Navigation */
.nav-link {
    @apply text-gray-600 hover:text-primary transition-colors duration-200 font-medium;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg;
    background-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
}

.btn-secondary {
    @apply border-2 border-primary text-primary px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:bg-primary hover:text-white;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-accent {
    @apply text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 hover:shadow-lg;
    background: var(--bg-gradient-tech);
}

/* Cards */
.card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 transition-all duration-200 hover:shadow-md;
}

.card:hover {
    transform: translateY(-2px);
}

/* Service Cards */
.service-card {
    @apply bg-white rounded-xl p-8 shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-lg;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

/* Tech Icons */
.tech-icon {
    @apply w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-xl;
    background: var(--bg-gradient-tech);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        @apply px-6;
    }

    .hero-gradient {
        @apply py-16;
    }

    .service-card {
        @apply p-6;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
        line-height: 1.1;
    }

    h2 {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    h3 {
        font-size: 1.25rem;
    }

    .btn-primary,
    .btn-secondary,
    .btn-accent {
        @apply px-4 py-3 text-sm;
    }

    .hero-gradient {
        @apply py-12;
    }

    .service-card {
        @apply p-4;
    }

    .tech-icon {
        @apply w-10 h-10 text-lg;
    }

    .stat-number {
        @apply text-3xl;
    }

    .logo-text {
        @apply text-lg;
    }

    .nav-link {
        @apply text-base;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .container {
        @apply px-4;
    }

    .btn-primary,
    .btn-secondary,
    .btn-accent {
        @apply px-3 py-2 text-sm;
    }

    .service-card {
        @apply p-3;
    }

    .tech-icon {
        @apply w-8 h-8 text-base;
    }

    .logo-icon {
        @apply w-8 h-8 text-base;
    }

    .logo-text {
        @apply text-base;
    }

    .feature-list {
        @apply space-y-2;
    }

    .feature-item {
        @apply text-sm;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-icon,
    .tech-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .fixed,
    .btn-primary,
    .btn-secondary,
    .btn-accent,
    footer {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero-gradient {
        background: white !important;
        color: black !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    @apply opacity-50 pointer-events-none;
}

/* Form Styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

.form-textarea {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-vertical;
    min-height: 120px;
}

/* Success/Error Messages */
.alert-success {
    @apply bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg;
}

.alert-error {
    @apply bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg;
}

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles for Accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Reduced Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .fade-in-up {
        animation: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary,
    .btn-secondary,
    .btn-accent {
        border: 2px solid;
    }

    .nav-link:hover {
        text-decoration: underline;
    }
}

/* Logo and Branding */
.logo {
    @apply flex items-center space-x-3;
}

.logo-icon {
    @apply w-10 h-10 rounded-lg flex items-center justify-center text-white font-bold text-xl;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
}

.logo-text {
    @apply text-xl font-bold;
    color: var(--primary-color);
}

/* Hero Section */
.hero-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--accent-blue) 100%);
}

.hero-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
}

/* Service Icons */
.service-icon-callcenter {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.service-icon-developers {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.service-icon-cloud {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.service-icon-support {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Stats/Numbers */
.stat-number {
    @apply text-4xl font-bold;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Feature Lists */
.feature-list {
    @apply space-y-3;
}

.feature-item {
    @apply flex items-start space-x-3;
}

.feature-icon {
    @apply w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold mt-0.5;
    background: var(--accent-green);
}

/* Technology Stack */
.tech-stack {
    @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.tech-item {
    @apply bg-white rounded-lg p-4 text-center shadow-sm border border-gray-100 transition-all duration-200 hover:shadow-md;
}

.tech-item:hover {
    transform: translateY(-2px);
}

/* CTA Sections */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.cta-pattern {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
}
