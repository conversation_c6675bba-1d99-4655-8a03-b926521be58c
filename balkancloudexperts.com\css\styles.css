/* Balkan Cloud Experts - Professional Tech Styles */
/* Modern, clean design for B2B cloud services */

[x-cloak] { 
    display: none !important;
}

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Container fallback for non-Tailwind environments */
.container {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Grid fallbacks */
.grid {
    display: grid;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
}

.gap-4 {
    gap: 1rem;
}

.gap-8 {
    gap: 2rem;
}

.gap-12 {
    gap: 3rem;
}

/* Responsive grid */
@media (min-width: 768px) {
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .md\\:grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .md\\:grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Common utility classes */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.text-center {
    text-align: center;
}

.text-white {
    color: white;
}

.text-primary {
    color: var(--primary-color);
}

.text-gray-200 {
    color: #e5e7eb;
}

.text-gray-300 {
    color: #d1d5db;
}

.text-gray-600 {
    color: #4b5563;
}

.text-gray-700 {
    color: #374151;
}

.text-cyan-300 {
    color: #67e8f9;
}

.text-sm {
    font-size: 0.875rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

/* Responsive text sizes */
@media (min-width: 768px) {
    .md\\:text-2xl {
        font-size: 1.5rem;
    }

    .md\\:text-4xl {
        font-size: 2.25rem;
    }

    .md\\:text-6xl {
        font-size: 3.75rem;
    }
}

.bg-white {
    background-color: white;
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.bg-accent-blue {
    background-color: var(--accent-blue);
}

.border {
    border-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-100 {
    border-color: #f3f4f6;
}

.border-gray-700 {
    border-color: #374151;
}

.hover\\:text-white:hover {
    color: white;
}

.hover\\:text-primary:hover {
    color: var(--primary-color);
}

.hover\\:bg-gray-100:hover {
    background-color: #f3f4f6;
}

.mt-1 {
    margin-top: 0.25rem;
}

.mt-8 {
    margin-top: 2rem;
}

.pt-8 {
    padding-top: 2rem;
}

.w-6 {
    width: 1.5rem;
}

.h-6 {
    height: 1.5rem;
}

.inline-block {
    display: inline-block;
}

.fixed {
    position: fixed;
}

.top-0 {
    top: 0;
}

.w-full {
    width: 100%;
}

.z-50 {
    z-index: 50;
}

.hidden {
    display: none;
}

.flex-col {
    flex-direction: column;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.pb-4 {
    padding-bottom: 1rem;
}

.pt-4 {
    padding-top: 1rem;
}

/* Additional utilities */
.w-1 {
    width: 0.25rem;
}

.h-1 {
    height: 0.25rem;
}

.rounded-full {
    border-radius: 50%;
}

.opacity-60 {
    opacity: 0.6;
}

.mt-12 {
    margin-top: 3rem;
}

.max-w-2xl {
    max-width: 42rem;
}

.max-w-3xl {
    max-width: 48rem;
}

/* Responsive utilities */
@media (min-width: 640px) {
    .sm\\:flex-row {
        flex-direction: row;
    }
}

@media (min-width: 1024px) {
    .lg\\:flex {
        display: flex;
    }

    .lg\\:hidden {
        display: none;
    }
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.space-y-8 > * + * {
    margin-top: 2rem;
}

.space-x-3 > * + * {
    margin-left: 0.75rem;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-x-8 > * + * {
    margin-left: 2rem;
}

.max-w-4xl {
    max-width: 56rem;
}

.max-w-6xl {
    max-width: 72rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

.mb-16 {
    margin-bottom: 4rem;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.pt-24 {
    padding-top: 6rem;
}

.pb-16 {
    padding-bottom: 4rem;
}

:root {
    /* Primary Brand Colors - Tech/Cloud focused */
    --primary-color: #0f172a;        /* Slate 900 - Professional dark */
    --primary-light: #1e293b;        /* Slate 800 */
    --primary-dark: #020617;         /* Slate 950 */
    
    /* Accent Colors */
    --accent-blue: #0ea5e9;          /* Sky 500 - Cloud/Tech */
    --accent-cyan: #06b6d4;          /* Cyan 500 - Digital */
    --accent-green: #10b981;         /* Emerald 500 - Success */
    
    /* Text Colors */
    --text-dark: #1f2937;            /* Gray 800 */
    --text-medium: #4b5563;          /* Gray 600 */
    --text-light: #6b7280;           /* Gray 500 */
    --text-muted: #9ca3af;           /* Gray 400 */
    
    /* Background Colors */
    --bg-primary: #ffffff;           /* White */
    --bg-secondary: #f8fafc;         /* Slate 50 */
    --bg-tertiary: #f1f5f9;          /* Slate 100 */
    --bg-dark: #0f172a;              /* Slate 900 */
    
    /* Border Colors */
    --border-light: #e2e8f0;         /* Slate 200 */
    --border-medium: #cbd5e1;        /* Slate 300 */
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-dark);
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2rem;
    font-weight: 600;
}

h3 {
    font-size: 1.5rem;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color);
}

.text-accent-blue {
    color: var(--accent-blue);
}

.text-accent-cyan {
    color: var(--accent-cyan);
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-accent-blue {
    background-color: var(--accent-blue);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.bg-gradient-tech {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
}

.border-primary {
    border-color: var(--primary-color);
}

/* Navigation */
.nav-link {
    color: #6b7280;
    font-weight: 500;
    transition: color 0.2s ease;
    text-decoration: none;
}

.nav-link:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.btn-secondary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
    cursor: pointer;
}

.btn-accent:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Cards */
.card {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid #f3f4f6;
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* Service Cards */
.service-card {
    background-color: white;
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Tech Icons */
.tech-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .hero-gradient {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .service-card {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2rem;
        line-height: 1.1;
    }

    h2 {
        font-size: 1.75rem;
        line-height: 1.2;
    }

    h3 {
        font-size: 1.25rem;
    }

    .btn-primary,
    .btn-secondary,
    .btn-accent {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .hero-gradient {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .service-card {
        padding: 1rem;
    }

    .tech-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1.125rem;
    }

    .stat-number {
        font-size: 1.875rem;
    }

    .logo-text {
        font-size: 1.125rem;
    }

    .nav-link {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .btn-primary,
    .btn-secondary,
    .btn-accent {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    .service-card {
        padding: 0.75rem;
    }

    .tech-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }

    .logo-icon {
        width: 2rem;
        height: 2rem;
        font-size: 1rem;
    }

    .logo-text {
        font-size: 1rem;
    }

    .feature-list {
        gap: 0.5rem;
    }

    .feature-item {
        font-size: 0.875rem;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-icon,
    .tech-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles */
@media print {
    .fixed,
    .btn-primary,
    .btn-secondary,
    .btn-accent,
    footer {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero-gradient {
        background: white !important;
        color: black !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.fade-in-up {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    font-size: 1rem;
    background-color: white;
}

.form-input:focus {
    outline: none;
    border-color: transparent;
    box-shadow: 0 0 0 2px var(--accent-blue);
}

.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    font-size: 1rem;
    background-color: white;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-textarea:focus {
    outline: none;
    border-color: transparent;
    box-shadow: 0 0 0 2px var(--accent-blue);
}

/* Success/Error Messages */
.alert-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
}

.alert-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
}

/* Performance Optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles for Accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Reduced Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .fade-in-up {
        animation: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary,
    .btn-secondary,
    .btn-accent {
        border: 2px solid;
    }

    .nav-link:hover {
        text-decoration: underline;
    }
}

/* Logo and Branding */
.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.25rem;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
}

.logo-text {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Hero Section */
.hero-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--accent-blue) 100%);
}

.hero-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
}

/* Service Icons */
.service-icon-callcenter {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.service-icon-developers {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.service-icon-cloud {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.service-icon-support {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* Stats/Numbers */
.stat-number {
    font-size: 2.25rem;
    font-weight: bold;
    background: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-cyan) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Feature Lists */
.feature-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.feature-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
    margin-top: 0.125rem;
    background: var(--accent-green);
    flex-shrink: 0;
}

/* Technology Stack */
.tech-stack {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media (min-width: 768px) {
    .tech-stack {
        grid-template-columns: repeat(4, 1fr);
    }
}

.tech-item {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    border: 1px solid #f3f4f6;
    transition: all 0.2s ease;
}

.tech-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* CTA Sections */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.cta-pattern {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.2) 0%, transparent 50%);
}
